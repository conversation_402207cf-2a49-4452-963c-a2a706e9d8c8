import { httpService } from '@/services/http.service'

import { Params } from '@/types/http.type'
import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { MedicineType, Product, ProductAgeGroup, ProductCategory } from '@/payload-types'
import { PaginatedDocs } from 'payload'
import { ProductCategoriesV2 } from '../types'

// SERVER / CLIENT
class ProductV2Service {
  private static instance: ProductV2Service

  private constructor() {}

  public static getInstance(): ProductV2Service {
    if (!ProductV2Service.instance) {
      ProductV2Service.instance = new ProductV2Service()
    }
    return ProductV2Service.instance
  }

  public async getProductV2BySlug({
    slug,
    options = {},
    params = {},
  }: {
    slug: string
    options?: RequestInit
    params?: Params
  }): Promise<Product | null> {
    const data = await httpService.getWithFetch<Product | null>(
      `/${API_ENDPOINTS.products_v2_api}/details/${slug}`,
      params,
      options,
    )
    return data
  }

  public async getProductsV2({
    options = {},
    params = {},
  }: {
    options?: RequestInit
    params?: Params
  }): Promise<PaginatedDocs<Product> | null> {
    const data = await httpService.getWithFetch<PaginatedDocs<Product> | null>(
      `/${API_ENDPOINTS.products_v2_api}/filtered`,
      params,
      options,
    )
    return data
  }

  public async getCategoriesV2({
    options = {},
    params = {},
  }: {
    options?: RequestInit
    params?: Params
  }): Promise<PaginatedDocs<ProductCategoriesV2> | null> {
    const data = await httpService.getWithFetch<PaginatedDocs<ProductCategoriesV2> | null>(
      `/${API_ENDPOINTS.product_categories_api}`,
      params,
      options,
    )
    return data
  }

  public async getProductNestedCategories({
    categoryId,
    params = {},
    options = {},
  }: {
    categoryId: string
    params?: Params
    options?: RequestInit
  }): Promise<PaginatedDocs<ProductCategory> | null> {
    const url = `/${API_ENDPOINTS.product_categories_api}/nested-categories/${categoryId}`

    const data = await httpService.getWithFetch<PaginatedDocs<ProductCategory>>(
      url,
      params,
      options,
    )

    return data
  }

  public async getProductAgeGroup({
    options = {},
    params = {},
  }: {
    options?: RequestInit
    params?: Params
  }): Promise<PaginatedDocs<ProductAgeGroup> | null> {
    const data = await httpService.getWithFetch<PaginatedDocs<ProductAgeGroup>>(
      `/${API_ENDPOINTS.product_age_groups_api}`,
      params,
      options,
    )
    return data
  }

  public async getMedicineType({
    options = {},
    params = {},
  }: {
    options?: RequestInit
    params?: Params
  }): Promise<PaginatedDocs<MedicineType> | null> {
    const data = await httpService.getWithFetch<PaginatedDocs<MedicineType>>(
      `/${API_ENDPOINTS.medicine_type_api}`,
      params,
      options,
    )
    return data
  }
}

export const productV2Service = ProductV2Service.getInstance()
