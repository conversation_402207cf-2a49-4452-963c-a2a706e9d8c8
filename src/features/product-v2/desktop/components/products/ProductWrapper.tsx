'use client'
import { ProductV2TypeEnum } from '@/features/product-v2/enums'
import { useGetInfiniteProductsV2 } from '@/features/product-v2/hooks/query/useGetInfiniteProductsV2'
import { useLocale } from 'next-intl'
import FeaturedProductList from './FeaturedProductList'
import ProductList from './ProductList'
import { useState, useEffect, useMemo } from 'react'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import InfiniteScroll from 'react-infinite-scroll-component'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { IFilterProduct, MedicineTypeExtend, ProductAgeGroupExtend } from '../modals/FilterModal'

const ProductWrapper: React.FC = () => {
  const locale = useLocale()
  const { getAllSearchQueries } = useSearchQuery()

  const [typeFilter, setTypeFilter] = useState<ProductV2TypeEnum[]>([ProductV2TypeEnum.MEDICINE])

  const { category, categoryIds } = getAllSearchQueries()

  const [filterOptions, setFilterOptions] = useState<
    (MedicineTypeExtend | ProductAgeGroupExtend)[]
  >([])

  // Parse categoryIds from URL to get selected categories
  const selectedCategoryIds = useMemo(() => {
    if (categoryIds && typeof categoryIds === 'string') {
      try {
        const parsedIds = JSON.parse(decodeURIComponent(categoryIds)) as Array<{
          id: string
          name: string
        }>
        return parsedIds.map((item) => item.id)
      } catch (error) {
        console.error('Error parsing categoryIds from URL:', error)
        return []
      }
    }
    return []
  }, [categoryIds])

  // Update typeFilter when category changes in URL
  useEffect(() => {
    if (category && Object.values(ProductV2TypeEnum).includes(category as ProductV2TypeEnum)) {
      setTypeFilter([category as ProductV2TypeEnum])
    } else {
      // Default to MEDICINE if no valid category in URL
      setTypeFilter([ProductV2TypeEnum.MEDICINE])
    }
  }, [category])

  const { isLoading, productsV2: featuredProduct } = useGetInfiniteProductsV2({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      locale: locale ?? 'vi',
      limit: 12,
      categories: selectedCategoryIds,
      where: {
        and: [
          {
            featured: {
              equals: true,
            },
          },
          {
            type: {
              in: typeFilter,
            },
          },
        ],
      },
    },
  })

  const {
    isLoading: isProductListLoading,
    productsV2: productList,
    fetchNextPage,
    hasNextPage,
  } = useGetInfiniteProductsV2({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      locale: locale ?? 'vi',
      limit: 18,
      categories: selectedCategoryIds,
      where: {
        and: [
          {
            type: {
              in: typeFilter,
            },
          },
          {
            ageGroups: {
              in: filterOptions.filter((item) => item.type === 'age').map((item) => item.id),
            },
          },
          {
            medicineType: {
              in: filterOptions.filter((item) => item.type === 'medicine').map((item) => item.id),
            },
          },
        ],
      },
    },
  })

  const dataProductFeatured = featuredProduct?.pages[0]?.docs
  // Flatten all pages for infinite scroll
  const allProductListData = productList?.pages.flatMap((page) => page.docs) ?? []

  const applyFilter = (data: IFilterProduct) => {
    const { params } = data
    setFilterOptions(params)
  }

  return (
    <>
      <FeaturedProductList featureProduct={dataProductFeatured ?? []} isLoading={isLoading} />

      {/* Product List with Infinite Scroll */}
      {isProductListLoading && allProductListData.length === 0 ? (
        <ProductList
          totalData={productList?.pages[0]?.totalDocs ?? 0}
          isLoading={true}
          productListData={[]}
          applyFilter={applyFilter}
        />
      ) : (
        productList &&
        productList.pages[0]?.docs.length > 0 && (
          <InfiniteScroll
            dataLength={allProductListData.length}
            next={() => {
              if (hasNextPage) {
                fetchNextPage()
              }
            }}
            hasMore={!!hasNextPage}
            loader={
              <div className="flex items-center justify-center py-4">
                <Spinner />
              </div>
            }
            scrollThreshold={0.8}
          >
            <ProductList
              totalData={productList?.pages[0]?.totalDocs ?? 0}
              isLoading={false}
              productListData={allProductListData}
              applyFilter={applyFilter}
            />
          </InfiniteScroll>
        )
      )}
    </>
  )
}

export default ProductWrapper
